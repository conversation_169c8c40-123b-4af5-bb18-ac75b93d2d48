/* 
 * SOLUTION RADICALE POUR TOUS LES PROBLÈMES DE SCROLL
 * Ce fichier corrige définitivement tous les conflits de scroll
 */

/* ===== SCROLL GLOBAL NATIF ===== */
html {
    scroll-behavior: smooth !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
}

body {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    -webkit-overflow-scrolling: touch !important;
    overscroll-behavior-y: none !important;
}

/* ===== CORRECTION MENU MOBILE ===== */
.mobile-menu-dropdown {
    max-height: calc(100vh - 80px) !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    -webkit-overflow-scrolling: touch !important;
    overscroll-behavior: contain !important;
    scrollbar-width: thin !important;
    scrollbar-color: #6689EF #1B1C1D !important;
}

.mobile-menu-dropdown::-webkit-scrollbar {
    width: 6px !important;
}

.mobile-menu-dropdown::-webkit-scrollbar-track {
    background: #1B1C1D !important;
}

.mobile-menu-dropdown::-webkit-scrollbar-thumb {
    background: #6689EF !important;
    border-radius: 3px !important;
}

.mobile-menu-dropdown::-webkit-scrollbar-thumb:hover {
    background: #2190F6 !important;
}

/* ===== CORRECTION CHAT ET COMPOSANTS INTERNES ===== */
.chat-container,
.ai-chat-container,
.chat-messages {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    -webkit-overflow-scrolling: touch !important;
    overscroll-behavior: contain !important;
    scroll-behavior: smooth !important;
}

/* ===== CORRECTION PANELS ET MODALES ===== */
.ai-tools-panel .tools-container,
.modal-content,
.dropdown-menu {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    -webkit-overflow-scrolling: touch !important;
    overscroll-behavior: contain !important;
}

/* ===== DÉSACTIVATION COMPLÈTE DE LENIS ===== */
[data-lenis-prevent] {
    overscroll-behavior: auto !important;
}

.lenis {
    pointer-events: none !important;
}

/* ===== SCROLL MOBILE ET TABLETTE ===== */
@media (max-width: 1024px) {
    * {
        -webkit-overflow-scrolling: touch !important;
    }
    
    .mobile-scroll-fix,
    .tablet-scroll-fix {
        overflow-y: auto !important;
        overscroll-behavior-y: none !important;
    }
}

/* ===== CORRECTION SPÉCIFIQUE POUR LES MENUS DÉROULANTS ===== */
.dropdown-content,
.select-options,
.menu-items {
    max-height: 300px !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    -webkit-overflow-scrolling: touch !important;
}

/* ===== PRÉVENTION DES CONFLITS AVEC LES ANIMATIONS ===== */
.gsap-scroll-trigger {
    pointer-events: none !important;
}

/* ===== CORRECTION POUR LES ÉLÉMENTS AVEC POSITION FIXED ===== */
.fixed-element {
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
}

/* ===== SCROLL BARS PERSONNALISÉES POUR TOUS LES ÉLÉMENTS ===== */
* {
    scrollbar-width: thin;
    scrollbar-color: #6689EF #1B1C1D;
}

*::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

*::-webkit-scrollbar-track {
    background: #1B1C1D;
}

*::-webkit-scrollbar-thumb {
    background: #6689EF;
    border-radius: 3px;
}

*::-webkit-scrollbar-thumb:hover {
    background: #2190F6;
}

/* ===== CLASSES UTILITAIRES POUR FORCER LE SCROLL ===== */
.force-scroll {
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
}

.force-no-scroll {
    overflow: hidden !important;
}

.force-scroll-x {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
}

.force-scroll-y {
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
}

/* ===== CORRECTION POUR LES ÉLÉMENTS AVEC Z-INDEX ÉLEVÉ ===== */
.z-50, .z-40, .z-30 {
    transform: translateZ(0) !important;
}

/* ===== PRÉVENTION DES BUGS DE SCROLL SUR SAFARI ===== */
@supports (-webkit-appearance: none) {
    body {
        -webkit-overflow-scrolling: touch !important;
    }
}

/* ===== CORRECTION POUR LES ÉLÉMENTS AVEC BACKDROP-BLUR ===== */
.backdrop-blur-lg,
.backdrop-blur-md,
.backdrop-blur-sm {
    -webkit-backdrop-filter: blur(16px) !important;
    backdrop-filter: blur(16px) !important;
    transform: translateZ(0) !important;
}

/* ===== RESET COMPLET DES STYLES DE SCROLL PROBLÉMATIQUES ===== */
.lenis-smooth {
    scroll-behavior: auto !important;
}

[data-lenis] {
    scroll-behavior: auto !important;
}

/* ===== CORRECTION FINALE POUR TOUS LES CONTENEURS ===== */
.container,
.max-w-7xl,
.max-w-6xl,
.max-w-5xl,
.max-w-4xl {
    overflow-x: hidden !important;
}

/* ===== STYLES SPÉCIFIQUES POUR LE HEADER ===== */
nav {
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
}

/* ===== CORRECTION POUR LES ANIMATIONS GSAP ===== */
[data-gsap] {
    will-change: auto !important;
}

/* ===== PRÉVENTION DES CONFLITS AVEC REACT ===== */
#root {
    overflow-x: hidden !important;
    min-height: 100vh !important;
}

/* ===== CORRECTION POUR LES ÉLÉMENTS AVEC TRANSFORM ===== */
.transform {
    transform: translateZ(0) !important;
}

/* ===== STYLES DE DEBUG (À SUPPRIMER EN PRODUCTION) ===== */
.debug-scroll {
    border: 2px solid red !important;
    background: rgba(255, 0, 0, 0.1) !important;
}

/* ===== CORRECTION POUR LES ÉLÉMENTS AVEC POSITION STICKY ===== */
.sticky {
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
}

/* ===== CORRECTION FINALE POUR TOUS LES PROBLÈMES DE SCROLL ===== */
html, body, #root, .App {
    scroll-behavior: smooth !important;
    overflow-x: hidden !important;
    -webkit-overflow-scrolling: touch !important;
}

/* ===== PRÉVENTION DES ERREURS JSX ===== */
[jsx] {
    display: none !important;
}

/* Correction pour les attributs jsx incorrects */
*[jsx="true"],
*[jsx="false"] {
    display: revert !important;
}
