import React, { useEffect } from 'react';
import { HashRouter, Routes, Route } from 'react-router-dom';
import Header from './components/Header';
import Footer from './components/Footer';
import Home from './pages/Home';
import About from './pages/About';
import Skills from './pages/Skills';
import Portfolio from './pages/Portfolio';
import Apps from './pages/Apps';
import Services from './pages/Services';
import Contact from './pages/Contact';
import DynamicBackground from './components/DynamicBackground';
import ScrollToTop from './components/ScrollToTop';
import ScrollToTopButton from "./components/ScrollToTopButton";
import {
    AdvisorNutrition,
    AssistantParcours,
    BudgetSage,
    DashboardMeteo,
    DeveloperInspector,
    PromptFlowDashboard
} from './pages/projects';
import Assistance from './pages/Assistance';
import {
    PackDiagnostic,
    PackReparation,
    PackRestauration,
    PackComplete,
    PackCustom
} from './pages/assistance-packs';
import "./styles/glowing-card.css";
import "./styles/sticky-header.css";
import "./styles/footer-3d.css";
import "./styles/scrollbar-hide.css";

const App: React.FC = () => {
    useEffect(() => {
        // DÉSACTIVATION COMPLÈTE DE LENIS POUR RÉSOUDRE LES PROBLÈMES DE SCROLL
        // Utilisation du scroll natif uniquement

        // Register ScrollTrigger plugin seulement
        // @ts-ignore
        gsap.registerPlugin(ScrollTrigger);

        // Configuration ScrollTrigger pour tous les appareils
        // @ts-ignore
        ScrollTrigger.config({
            autoRefreshEvents: "visibilitychange,DOMContentLoaded,load"
        });

        // Forcer le scroll natif sur tous les éléments
        document.documentElement.style.scrollBehavior = 'smooth';
        document.body.style.overflowY = 'auto';
        document.body.style.overflowX = 'hidden';

        return () => {
            // Nettoyage
            document.documentElement.style.scrollBehavior = '';
        };
    }, []);

    return (
        <HashRouter>
            <div className="relative min-h-screen w-full overflow-x-hidden mobile-scroll-fix tablet-scroll-fix">
                <DynamicBackground />
                <div className="relative z-10">
                    <ScrollToTop />
                    <Header />
                    <ScrollToTopButton />
                    <main className="pt-20">
                        <Routes>
                            <Route path="/" element={<Home />} />
                            <Route path="/a-propos" element={<About />} />
                            <Route path="/competences" element={<Skills />} />
                            <Route path="/portfolio" element={<Portfolio />} />
                            <Route path="/apps" element={<Apps />} />
                            <Route path="/assistance" element={<Assistance />} />
                            <Route path="/services" element={<Services />} />
                            <Route path="/contact" element={<Contact />} />

                            {/* Routes des projets */}
                            <Route path="/projects/advisor-nutrition" element={<AdvisorNutrition />} />
                            <Route path="/projects/assistant-parcours" element={<AssistantParcours />} />
                            <Route path="/projects/budget-sage" element={<BudgetSage />} />
                            <Route path="/projects/dashboard-meteo" element={<DashboardMeteo />} />
                            <Route path="/projects/developer-inspector" element={<DeveloperInspector />} />
                            <Route path="/projects/promptflow-dashboard" element={<PromptFlowDashboard />} />

                            {/* Routes des packs d'assistance */}
                            <Route path="/assistance/pack-diagnostic" element={<PackDiagnostic />} />
                            <Route path="/assistance/pack-reparation" element={<PackReparation />} />
                            <Route path="/assistance/pack-restauration" element={<PackRestauration />} />
                            <Route path="/assistance/pack-complete" element={<PackComplete />} />
                            <Route path="/assistance/pack-custom" element={<PackCustom />} />
                        </Routes>
                    </main>
                    <Footer />
                </div>
            </div>
        </HashRouter>
    );
};

export default App;
